from pyspark.sql import SparkSession
from pyspark.sql import functions as sf
from pyspark import SparkContext, SparkConf
import os
import json


# Create SparkConf and SparkContext
conf = SparkConf().setAppName("Word Count").setMaster("local[*]")
sc = SparkContext(conf=conf)

# Create SparkSession from existing SparkContext
spark = SparkSession(sc)

# Display SparkContext information (already created above)
print(f"SparkContext: {sc}")
print(f"Spark version: {sc.version}")
print(f"Python version: {sc.pythonVer}")
print(f"Master: {sc.master}")
print(f"Application name: {sc.appName}")
print(f"Default parallelism: {sc.defaultParallelism}")

entity_list = [
    'modi', 'rahul', 'jaitley', 'sonia', 'lalu', 'nitish', 'farooq', 'sushma', 'tharoor', 'smriti', 'mamata', 'karunanidhi', 'kejriwal', 'sidhu', 'yogi', 'mayawati', 'akh<PERSON>h', 'chandrababu', 'chidambaram', 'fadnavis', 'uddhav', 'pawar'
]

data = spark.read.option("multiline", "true").json("newsdata")


sample = data.take(15)
sample

articles_rdd = data.rdd.map(lambda row: row.asDict())

sample = articles_rdd.take(5)
sample[0]

def get_entity_id_pair(article):
    
    article_id = article.get('article_id', '')
    [title, description, article_body] = [article.get('title', ''), article.get('description', ''), article.get('article_body', '')]
    all_text = ' '.join([title, description, article_body]).lower()
    if article_id == '' or all_text == '':
        return []

    
    all_words = list(map(lambda word: (word, 1),list(filter(lambda x: x in entity_list, all_text.split(' ')))))
    # print(type(all_words), all_words)
    # all_words_rdd = sc.parallelize(all_words)
    # all_words_rdd.reduceByKey(lambda a, b: a + b)
    pairs = [(article_id, word, count) for word, count in all_words]
    return pairs

get_entity_id_pair(sample[0])

pairwaise_rdd = articles_rdd.flatMap(get_entity_id_pair).filter(lambda _1,x, _2: x is not None).reduceByKey(lambda a, b: a + b).distinct()

pairwaise_rdd.take(5)

# filtered_rdd = data.filter(lambda line: ['"description"', '"article_id"'] in line)
# filtered_article_rdd = filtered_rdd.filter(lambda line: '"article_id"' in line)
# filtered_desp_rdd = filtered_rdd.filter(lambda line: '"description"' in line)

# def safe_parse(line):
#     try:
#         return json.loads(line)
#     except json.JSONDecodeError:
#         return None # Or return an empty dict {}



data_rdd = filtered_rdd.map(safe_parse).filter(lambda x: x is not None)

data_rdd = data.map(lambda line: json.loads(line) if line )


data_rdd.toDF().show()


data_rdd.take(5)

word_counts = data.flatMap(lambda line: line.lower().split(" ")) \
                   .filter(lambda word: word.strip() != "") \
                   .map(lambda word: (word, 1)) \
                   .reduceByKey(lambda a, b: a + b)

word_counts.toDF().show()

for word, count in word_counts.collect():
    print(f"{word}: {count}")

