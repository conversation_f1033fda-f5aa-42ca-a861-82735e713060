from pyspark.sql import SparkSession
from pyspark.sql import functions as sf
from pyspark import SparkContext, SparkConf
import os
import json


# Create SparkConf and SparkContext
conf = SparkConf().setAppName("Word Count").setMaster("local[*]")
sc = SparkContext(conf=conf)

# Create SparkSession from existing SparkContext
spark = SparkSession(sc)

# Display SparkContext information (already created above)
print(f"SparkContext: {sc}")
print(f"Spark version: {sc.version}")
print(f"Python version: {sc.pythonVer}")
print(f"Master: {sc.master}")
print(f"Application name: {sc.appName}")
print(f"Default parallelism: {sc.defaultParallelism}")

data = sc.textFile("newsdata/*.json")


sample = data.take(15)
sample

filtered_rdd = data.filter(lambda line: ['"description"', '"article_id"'] in line)
# filtered_article_rdd = filtered_rdd.filter(lambda line: '"article_id"' in line)
# filtered_desp_rdd = filtered_rdd.filter(lambda line: '"description"' in line)

def safe_parse(line):
    try:
        return json.loads(line)
    except json.JSONDecodeError:
        return None # Or return an empty dict {}



data_rdd = filtered_rdd.map(safe_parse).filter(lambda x: x is not None)

data_rdd = data.map(lambda line: json.loads(line) if line )


data_rdd.toDF().show()

word_counts = data.flatMap(lambda line: line.lower().split(" ")) \
                   .filter(lambda word: word.strip() != "") \
                   .map(lambda word: (word, 1)) \
                   .reduceByKey(lambda a, b: a + b)

word_counts.toDF().show()

for word, count in word_counts.collect():
    print(f"{word}: {count}")

