from pyspark.sql import SparkSession
from pyspark.sql import functions as sf
from pyspark import SparkContext, SparkConf
import os
import json


# Create SparkConf and SparkContext
conf = SparkConf().setAppName("Word Count").setMaster("local[*]")
sc = SparkContext(conf=conf)

# Create SparkSession from existing SparkContext
spark = SparkSession(sc)

# Display SparkContext information (already created above)
print(f"SparkContext: {sc}")
print(f"Spark version: {sc.version}")
print(f"Python version: {sc.pythonVer}")
print(f"Master: {sc.master}")
print(f"Application name: {sc.appName}")
print(f"Default parallelism: {sc.defaultParallelism}")

entity_list = ['modi', 'rahul', 'jaitley', 'sonia', 'lalu', 'nitish', 'farooq', 'sushma', 'tharoor', 'smriti', 'mamata', 'karunanidhi', 'kejriwal', 'sidhu', 'yogi', 'mayawati', 'akh<PERSON>h', 'chandrababu', 'chidambaram', 'fadnavis', 'uddhav', 'pawar']

data = spark.read.option("multiline", "true").json("newsdata")


sample = data.take(1)
sample



articles_rdd = data.rdd.map(lambda i : i.asDict())

sample_line = articles_rdd.take(1)

sample_line[0]

def get_entity_id_pair(article):
    
    article_id = article.get('article_id', '')
    [title, description, article_body] = [article.get('title', ''), article.get('description', ''), article.get('article_body', '')]
    all_text = ' '.join([title, description, article_body]).lower()
    if article_id == '' or all_text == '':
        return []

    
    all_words = list(map(lambda word: (word, 1),list(filter(lambda x: x in entity_list, all_text.split(' ')))))
    print(type(all_words), all_words)
    all_words_rdd = sc.parallelize(all_words)
    all_words_rdd.reduceByKey(lambda a, b: a + b)
    pairs = [(word, article_id) for word in all_words]
    return all_words_rdd
    

word_counts = get_entity_id_pair(sample_line[0])

word_counts.collect()

pairwise_rdd = articles_rdd.flatMap(get_entity_id_pair).filter(lambda x : x is not None).distinct()

pairwise_rdd.collect()

pairwise_df = spark.createDataFrame(pairwise_rdd, ["entity", "article_id"])


pairwise_df.show()


# filtered_rdd = data.filter(lambda line: '"description"' in line or '"article_id"' in line)



# filtered_rdd.take(10)

# data_rdd = data.map(lambda line: json.loads(line) if line )




word_counts = data.flatMap(lambda line: line.lower().split(" ")) \
                   .filter(lambda word: word.strip() != "") \
                   .map(lambda word: (word, 1)) \
                   .reduceByKey(lambda a, b: a + b)

word_counts.toDF().show()

for word, count in word_counts.collect():
    print(f"{word}: {count}")

# Process entities from articles to create (entity, article_id) pairs
# This addresses your requirement for processing each article with map operations

def extract_entity_article_pairs(json_obj):
    """
    Extract (entity, article_id) pairs from a JSON article object.
    Tokenizes text content and performs case-insensitive comparison.
    """
    if not isinstance(json_obj, dict):
        return []
    
    # Get article_id
    article_id = json_obj.get('article_id', '')
    if not article_id:
        return []
    
    # Get text content from multiple fields
    text_fields = ['title', 'description', 'article_body']
    all_text = []
    
    for field in text_fields:
        if field in json_obj and json_obj[field]:
            all_text.append(str(json_obj[field]))
    
    if not all_text:
        return []
    
    # Combine all text and tokenize (case-insensitive)
    combined_text = ' '.join(all_text).lower()
    
    # Tokenize: split by whitespace and remove punctuation
    import re
    tokens = re.findall(r'\b[a-zA-Z]+\b', combined_text)
    
    # Create (entity, article_id) pairs, removing duplicates within article
    entity_pairs = []
    seen_entities = set()
    
    for token in tokens:
        if token and len(token) > 2:  # Filter out very short words
            if token not in seen_entities:
                entity_pairs.append((token, article_id))
                seen_entities.add(token)
    
    return entity_pairs

# Apply the function using flatMap to create entity-article pairs
print("Processing articles to extract entity-article pairs...")
entity_article_rdd = json_rdd.flatMap(extract_entity_article_pairs)

print("\nSample entity-article pairs:")
sample_pairs = entity_article_rdd.take(10)
for entity, article_id in sample_pairs:
    print(f"Entity: '{entity}' -> Article ID: {article_id}")

print(f"\nTotal entity-article pairs: {entity_article_rdd.count()}")

# Remove duplicates across the entire dataset (no duplicate rows)
unique_entity_article_rdd = entity_article_rdd.distinct()
print(f"Unique entity-article pairs: {unique_entity_article_rdd.count()}")

# Alternative approach: Using for loop with collect() for smaller datasets
# WARNING: Only use this for small datasets as collect() brings all data to driver

print("=== For Loop Approach (for small datasets) ===")

# Take a small sample for demonstration
sample_articles = json_rdd.take(5)

entity_article_pairs = []

# Process each article with a for loop
for i, article in enumerate(sample_articles):
    print(f"\nProcessing article {i+1}:")
    
    if not isinstance(article, dict):
        continue
        
    article_id = article.get('article_id', '')
    title = article.get('title', '')
    
    print(f"  Article ID: {article_id}")
    print(f"  Title: {title[:100]}...")  # Show first 100 chars
    
    if article_id and title:
        # Tokenize title (case-insensitive)
        import re
        tokens = re.findall(r'\b[a-zA-Z]+\b', title.lower())
        
        # Create pairs for this article
        article_entities = set()  # Use set to avoid duplicates within article
        for token in tokens:
            if len(token) > 2:  # Filter short words
                article_entities.add(token)
        
        # Add to global list
        for entity in article_entities:
            entity_article_pairs.append((entity, article_id))
        
        print(f"  Extracted {len(article_entities)} unique entities")

print(f"\nTotal pairs from for loop: {len(entity_article_pairs)}")
print("Sample pairs:")
for entity, article_id in entity_article_pairs[:10]:
    print(f"  '{entity}' -> {article_id}")

# Create DataFrame from entity-article pairs
print("=== Creating DataFrame from Entity-Article Pairs ===")

try:
    # Convert RDD to DataFrame with proper column names
    entity_df = unique_entity_article_rdd.toDF(["entity", "article_id"])
    
    print(f"DataFrame created with {entity_df.count()} rows")
    print("\nSchema:")
    entity_df.printSchema()
    
    print("\nSample data:")
    entity_df.show(20, truncate=False)
    
    # Some analytics
    print("\n=== Analytics ===")
    print(f"Total unique entities: {entity_df.select('entity').distinct().count()}")
    print(f"Total unique articles: {entity_df.select('article_id').distinct().count()}")
    
    # Most common entities
    print("\nTop 10 most common entities:")
    entity_df.groupBy("entity").count().orderBy("count", ascending=False).show(10)
    
except Exception as e:
    print(f"Error creating DataFrame: {e}")
    print("Trying with sample data...")
    
    # Fallback: create DataFrame from sample
    sample_data = unique_entity_article_rdd.take(100)
    if sample_data:
        sample_df = spark.createDataFrame(sample_data, ["entity", "article_id"])
        print("Sample DataFrame:")
        sample_df.show(10)