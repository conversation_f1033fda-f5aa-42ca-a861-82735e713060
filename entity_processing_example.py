#!/usr/bin/env python3
"""
Example: Processing RDD with For Loop to Create Entity-Article Pairs

This example shows how to:
1. Run a for loop on filtered RDD
2. Process each article to extract entities
3. Create (entity, article_id) pairs with no duplicates
4. Perform case-insensitive tokenization
"""

from pyspark.sql import SparkSession
from pyspark import SparkContext, SparkConf
import json
import re

# Initialize Spark
conf = SparkConf().setAppName("Entity Processing").setMaster("local[*]")
sc = SparkContext(conf=conf)
spark = SparkSession(sc)

def extract_entity_article_pairs(json_obj):
    """
    Extract (entity, article_id) pairs from a JSON article object.
    Tokenizes text content and performs case-insensitive comparison.
    """
    if not isinstance(json_obj, dict):
        return []
    
    # Get article_id
    article_id = json_obj.get('article_id', '')
    if not article_id:
        return []
    
    # Get text content from multiple fields
    text_fields = ['title', 'description', 'article_body']
    all_text = []
    
    for field in text_fields:
        if field in json_obj and json_obj[field]:
            all_text.append(str(json_obj[field]))
    
    if not all_text:
        return []
    
    # Combine all text and tokenize (case-insensitive)
    combined_text = ' '.join(all_text).lower()
    
    # Tokenize: split by whitespace and remove punctuation
    tokens = re.findall(r'\b[a-zA-Z]+\b', combined_text)
    
    # Create (entity, article_id) pairs, removing duplicates within article
    entity_pairs = []
    seen_entities = set()
    
    for token in tokens:
        if token and len(token) > 2:  # Filter out very short words
            if token not in seen_entities:
                entity_pairs.append((token, article_id))
                seen_entities.add(token)
    
    return entity_pairs

def process_with_for_loop_approach(rdd, max_articles=10):
    """
    Process RDD using for loop approach (for small datasets only)
    """
    print("=== For Loop Approach ===")
    
    # Collect a small sample (WARNING: Don't do this for large datasets)
    sample_articles = rdd.take(max_articles)
    
    entity_article_pairs = []
    
    # Process each article with a for loop
    for i, article in enumerate(sample_articles):
        print(f"\nProcessing article {i+1}:")
        
        if not isinstance(article, dict):
            print("  Skipping non-dict object")
            continue
            
        article_id = article.get('article_id', '')
        title = article.get('title', '')
        
        print(f"  Article ID: {article_id}")
        print(f"  Title: {title[:100]}...")  # Show first 100 chars
        
        if article_id and title:
            # Tokenize title (case-insensitive)
            tokens = re.findall(r'\b[a-zA-Z]+\b', title.lower())
            
            # Create pairs for this article
            article_entities = set()  # Use set to avoid duplicates within article
            for token in tokens:
                if len(token) > 2:  # Filter short words
                    article_entities.add(token)
            
            # Add to global list
            for entity in article_entities:
                entity_article_pairs.append((entity, article_id))
            
            print(f"  Extracted {len(article_entities)} unique entities")
    
    return entity_article_pairs

def process_with_map_approach(rdd):
    """
    Process RDD using map/flatMap approach (recommended for large datasets)
    """
    print("=== Map/FlatMap Approach (Recommended) ===")
    
    # Apply the function using flatMap to create entity-article pairs
    entity_article_rdd = rdd.flatMap(extract_entity_article_pairs)
    
    print(f"Total entity-article pairs: {entity_article_rdd.count()}")
    
    # Remove duplicates across the entire dataset (no duplicate rows)
    unique_entity_article_rdd = entity_article_rdd.distinct()
    print(f"Unique entity-article pairs: {unique_entity_article_rdd.count()}")
    
    return unique_entity_article_rdd

# Example usage
if __name__ == "__main__":
    # Create sample data (simulating your JSON articles)
    sample_data = [
        {
            "article_id": "123",
            "title": "Budget brings hope to bamboo-rich northeast",
            "description": "Bamboo is essentially a type of grass but classification prevented exploitation",
            "article_body": "The allocation of funds for bamboo mission has raised hopes for industries"
        },
        {
            "article_id": "456", 
            "title": "Technology advances in artificial intelligence",
            "description": "AI technology is revolutionizing various industries",
            "article_body": "Machine learning and deep learning are key components of modern AI"
        },
        {
            "article_id": "789",
            "title": "Climate change impacts on agriculture", 
            "description": "Global warming affects crop yields worldwide",
            "article_body": "Farmers need to adapt to changing weather patterns and temperatures"
        }
    ]
    
    # Create RDD from sample data
    json_rdd = sc.parallelize(sample_data)
    
    print("Sample JSON data loaded into RDD")
    print(f"Number of articles: {json_rdd.count()}")
    
    # Method 1: For loop approach (for small datasets)
    for_loop_pairs = process_with_for_loop_approach(json_rdd, max_articles=5)
    
    print(f"\nFor loop results:")
    print(f"Total pairs: {len(for_loop_pairs)}")
    print("Sample pairs:")
    for entity, article_id in for_loop_pairs[:10]:
        print(f"  '{entity}' -> {article_id}")
    
    # Method 2: Map/FlatMap approach (recommended)
    map_pairs_rdd = process_with_map_approach(json_rdd)
    
    print(f"\nMap approach results:")
    sample_pairs = map_pairs_rdd.take(10)
    print("Sample pairs:")
    for entity, article_id in sample_pairs:
        print(f"  '{entity}' -> {article_id}")
    
    # Create DataFrame
    print("\n=== Creating DataFrame ===")
    entity_df = map_pairs_rdd.toDF(["entity", "article_id"])
    print("DataFrame created successfully!")
    entity_df.show(20, truncate=False)
    
    # Analytics
    print("\n=== Analytics ===")
    print(f"Total unique entities: {entity_df.select('entity').distinct().count()}")
    print(f"Total unique articles: {entity_df.select('article_id').distinct().count()}")
    
    print("\nTop entities by frequency:")
    entity_df.groupBy("entity").count().orderBy("count", ascending=False).show(10)
    
    # Stop Spark
    sc.stop()
    print("\nSpark context stopped.")
