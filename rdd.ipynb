from pyspark.sql import SparkSession
from pyspark.sql import functions as sf
from pyspark import SparkContext, SparkConf

# Initialize SparkContext directly (bypasses Spark Connect issues in PySpark 4.x)
import os

# Create SparkConf and SparkContext
conf = SparkConf().setAppName("Word Count").setMaster("local[*]")
sc = SparkContext(conf=conf)

# Create SparkSession from existing SparkContext
spark = SparkSession(sc)

spark

# Display SparkContext information (already created above)
print(f"SparkContext: {sc}")
print(f"Spark version: {sc.version}")
print(f"Python version: {sc.pythonVer}")
print(f"Master: {sc.master}")
print(f"Application name: {sc.appName}")
print(f"Default parallelism: {sc.defaultParallelism}")

# Example: Create an RDD and perform basic operations
# Create an RDD from a list
numbers = sc.parallelize([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])

# Basic RDD operations
print("Original RDD:", numbers.collect())
print("Count:", numbers.count())
print("First element:", numbers.first())
print("Take 3 elements:", numbers.take(3))

# Transformations
squared = numbers.map(lambda x: x ** 2)
print("Squared:", squared.collect())

even_numbers = numbers.filter(lambda x: x % 2 == 0)
print("Even numbers:", even_numbers.collect())

# Actions
total_sum = numbers.reduce(lambda a, b: a + b)
print("Sum:", total_sum)

# More advanced operations
print("\nAdvanced operations:")
print("Max value:", numbers.max())
print("Min value:", numbers.min())
print("Mean:", numbers.mean())
print("Standard deviation:", numbers.stdev())

# Stop SparkContext when done (optional - run this to clean up)
# sc.stop()
# print("SparkContext stopped")

# from pyspark import SparkContext, SparkConf

# # Set up the SparkContext to use all available local cores
# conf = SparkConf().setAppName("PartitionDemo").setMaster("local[*]")
# sc = SparkContext(conf=conf)

# Get the number of cores Spark is using
num_cores = sc.defaultParallelism
print(f"Spark is running with {num_cores} parallel cores/partitions.\n")

# Create a list of numbers
data = list(range(20)) # Numbers from 0 to 19
print(f"Original data: {data}\n")

# Create an RDD, which Spark will partition across the cores
rdd = sc.parallelize(data)

# Use glom() to see the contents of each partition
# This collects the data from each core's chunk into its own list
partitioned_data = rdd.glom().collect()

print("Data chunks as seen by each core:")
# Loop through the result to display each partition clearly
for i, partition in enumerate(partitioned_data):
    print(f"Core/Partition {i}: {partition}")

# Stop the SparkContext
sc.stop()

