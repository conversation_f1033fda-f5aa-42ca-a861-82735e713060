# from pyspark import SparkContext, SparkConf

# # Set up the SparkContext to use all available local cores
# conf = SparkConf().setAppName("PartitionDemo").setMaster("local[*]")
# sc = SparkContext(conf=conf)

# Get the number of cores Spark is using
num_cores = sc.defaultParallelism
print(f"Spark is running with {num_cores} parallel cores/partitions.\n")

# Create a list of numbers
data = list(range(20)) # Numbers from 0 to 19
print(f"Original data: {data}\n")

# Create an RDD, which Spark will partition across the cores
rdd = sc.parallelize(data)

# Use glom() to see the contents of each partition
# This collects the data from each core's chunk into its own list
partitioned_data = rdd.glom().collect()

print("Data chunks as seen by each core:")
# Loop through the result to display each partition clearly
for i, partition in enumerate(partitioned_data):
    print(f"Core/Partition {i}: {partition}")

# Stop the SparkContext
sc.stop()