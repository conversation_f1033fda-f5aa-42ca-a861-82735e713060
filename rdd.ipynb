from pyspark.sql import SparkSession
from pyspark.sql import functions as sf

# Initialize SparkSession (regular session, not Spark Connect)
spark = (
    SparkSession.builder
        .master("local[*]")
        .appName("Word Count")
        .config("spark.sql.adaptive.enabled", "true")
        .config("spark.sql.adaptive.coalescePartitions.enabled", "true")
        .getOrCreate()
)

spark

# Create a SparkContext from the SparkSession
sc = spark.sparkContext
print(f"SparkContext created: {sc}")
print(f"Spark version: {sc.version}")
print(f"Python version: {sc.pythonVer}")
print(f"Master: {sc.master}")

# Example: Create an RDD and perform basic operations
# Create an RDD from a list
numbers = sc.parallelize([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])

# Basic RDD operations
print("Original RDD:", numbers.collect())
print("Count:", numbers.count())
print("First element:", numbers.first())
print("Take 3 elements:", numbers.take(3))

# Transformations
squared = numbers.map(lambda x: x ** 2)
print("Squared:", squared.collect())

even_numbers = numbers.filter(lambda x: x % 2 == 0)
print("Even numbers:", even_numbers.collect())

# Actions
total_sum = numbers.reduce(lambda a, b: a + b)
print("Sum:", total_sum)